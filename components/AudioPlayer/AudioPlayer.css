.custom-audio-container {
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  padding: 20px 8px 8px;
  background: #F4F6F8;
  border-radius: 20px;
  outline: 1px solid #E5E7EB;
  outline-offset: -1px;
  width: 100%;
  position: relative;
}

.header {
  align-self: stretch;
  padding: 0px 12px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.artwork {
  width: 40px;
  height: 40px;
  border-radius: 6px;
}

.audio-text {
  flex: 1;
  padding: 0px 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.audio-text-content {
  flex: 1;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 1.57px;
}

.audio-title {
  align-self: stretch;
  height: 14px;
  position: relative;
  overflow: hidden;
}

.audio-title-text {
  width: 64px;
  left: -0.07px;
  top: 0.42px;
  position: absolute;
  justify-content: flex-start;
  color: #18181B;
  font-size: 12px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  line-height: 1;
}

.audio-subtitle {
  justify-content: flex-start;
  color: #A3A3A3;
  font-size: 12px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  line-height: 1;
}

.sound-wave-icon {
  text-align: center;
  justify-content: flex-start;
  color: #18181B;
  font-size: 18px;
  font-family: 'SF Pro Text', sans-serif;
  font-weight: 400;
  line-height: 1;
}

.custom-h5-player {
  box-shadow: none;
  background: transparent;
  width: 100%;
}

/* Progress Section - matches Figma HTML structure */
.rhap_progress-section {
  align-self: stretch;
  padding: 0px 12px;
  display: inline-flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
}

/* Time Display Styling */
.rhap_time {
  text-align: center;
  justify-content: flex-start;
  color: #A3A3A3;
  font-size: 12px;
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  line-height: 1;
  margin: 0;
  padding: 0;
}

/* Progress Bar Container */
.rhap_progress-container {
  flex: 1;
  padding: 3.15px;
  position: relative;
  display: inline-flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

/* Progress Bar Track */
.rhap_progress-bar {
  align-self: stretch;
  height: 5.12px;
  background: #E2E8F0;
  border-radius: 6px;
}

/* Progress Bar Fill */
.rhap_progress-indicator {
  width: 64px;
  height: 5.12px;
  left: 3.15px;
  top: 3.15px;
  position: absolute;
  background: #8B5CF6;
  border-radius: 6px;
}

.rhap_progress-filled {
  background: #8B5CF6;
  border-radius: 6px;
}

/* Controls Section */
.rhap_controls-section {
  align-self: stretch;
  padding: 0px 24px;
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
}

.rhap_main-controls {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  gap: 28px;
}

.rhap_button-clear {
  color: #18181B;
  background: none;
  border: none;
  font-family: 'SF Pro Text', sans-serif;
  font-weight: 600;
  text-align: center;
  justify-content: center;
}

.rhap_button-clear.rhap_rewind-button,
.rhap_button-clear.rhap_forward-button {
  font-size: 16px;
  line-height: 36px;
}

.rhap_button-clear.rhap_play-pause-button {
  font-size: 24px;
  line-height: 36px;
}

/* Custom time display to show remaining time with minus sign */
.rhap_time.rhap_duration::before {
  content: '-';
}