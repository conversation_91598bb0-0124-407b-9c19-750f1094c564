.custom-audio-container {
  display: flex;
  gap: 7px;
  color: #191A1D;
  padding: 20px 8px 8px;
  border-radius: 20px;
  flex-direction: column;
  box-shadow: none;
  background: #F4F6F8;
  width: 100%;
  position: relative;
  border: 1px solid #ECEDEF !important;
}

.header {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 9px;
  padding: 0px 12px;
  justify-content: center;
}

.artwork {
  border-radius: 6px;
}

.audio-text {
  display: flex;
  flex-direction: column;
  gap: 1.5px;
  flex: 1;
  padding: 0px 6px;
}

.audio-text .audio-title {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 600;
  font-size: 12.6px;
  line-height: 1.125em;
  letter-spacing: -2.55%;
  color: #191A1D;
}

.audio-text .audio-subtitle {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.18em;
  color: #8C9098;
}

.sound-wave-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #191A1D;
}

.custom-h5-player {
  box-shadow: none;
  background: transparent;
  padding: 0px 12px;
}

/* Progress Bar Styling */
.rhap_progress-bar {
  background-color: #E7E9EF;
  border-radius: 6px;
  height: 6px;
}

.rhap_progress-indicator {
  background-color: #7F56D9;
  border-radius: 6px;
}

.rhap_progress-filled {
  background-color: #7F56D9;
  border-radius: 6px;
}

/* Time Display Styling */
.rhap_time {
  font-family: 'Plus Jakarta Sans', sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 1.5em;
  color: #8C9098;
  text-align: center;
}

/* Controls Styling */
.rhap_controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 24px;
  gap: 7px;
}

.rhap_main-controls {
  display: flex;
  gap: 28px;
}

.rhap_button-clear {
  color: #191A1D;
  background: none;
  border: none;
  font-family: 'SF Pro Text', sans-serif;
  font-weight: 600;
}

.rhap_button-clear.rhap_rewind-button,
.rhap_button-clear.rhap_forward-button {
  font-size: 15.75px;
  line-height: 2.2em;
  letter-spacing: -2.04%;
}

.rhap_button-clear.rhap_play-pause-button {
  font-size: 25.2px;
  line-height: 1.375em;
  letter-spacing: -1.27%;
}

/* Progress Section Layout */
.rhap_progress-section {
  display: flex;
  align-items: center;
  gap: 7px;
  padding: 0px 12px;
}

/* Custom time display to show remaining time with minus sign */
.rhap_time.rhap_duration::before {
  content: '-';
}

/* Remove default margins and padding from time elements */
.rhap_time {
  margin: 0;
  padding: 0;
}