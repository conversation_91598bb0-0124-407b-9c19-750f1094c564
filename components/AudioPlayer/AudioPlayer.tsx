"use client";

import React, { useState } from "react";
import ReactH5AudioPlayer, {
  RHAP_UI,
} from "react-h5-audio-player";
import "react-h5-audio-player/src/styles.scss";
import "./AudioPlayer.css";
import Image from "next/image";

interface CustomAudioPlayerProps {
  title?: string;
  evaluatorName?: string;
  coverUrl?: string;
  src?: string;
}

export default function CustomAudioPlayer({
  title = "Test ID Name",
  evaluatorName = "Name",
  coverUrl = "/avatar.svg",
  src = '',
  ...props
}: CustomAudioPlayerProps) {
  const [, setIsPlaying] = useState(false);
  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);

  return (
    <div className="custom-audio-container">
      <div className="header">
        <Image
          src={coverUrl}
          width={50}
          height={50}
          alt={"User"}
        />
        <div className="audio-text">
          <div className="audio-title">{title}</div>
          <div className="audio-subtitle">Evaluator: {evaluatorName}</div>
        </div>
        <div className="sound-wave-icon">
          <svg width="16" height="19" viewBox="0 0 16 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.66254 18.1903C6.28188 18.1903 5.99427 17.8858 5.99427 17.5051V1.20441C5.99427 0.823753 6.28188 0.519225 6.66254 0.519225C7.05166 0.519225 7.35618 0.823753 7.35618 1.20441V17.5051C7.35618 17.8858 7.05166 18.1903 6.66254 18.1903ZM12.364 16.3124C11.9664 16.3124 11.6788 16.0078 11.6788 15.6356V3.07388C11.6788 2.70167 11.9664 2.38869 12.364 2.38869C12.7446 2.38869 13.0407 2.70167 13.0407 3.07388V15.6356C13.0407 16.0078 12.7446 16.3124 12.364 16.3124ZM3.82028 14.7813C3.43962 14.7813 3.14355 14.4768 3.14355 14.0961V4.61343C3.14355 4.23277 3.43962 3.91979 3.82028 3.91979C4.2094 3.91979 4.50547 4.23277 4.50547 4.61343V14.0961C4.50547 14.4768 4.2094 14.7813 3.82028 14.7813ZM9.51326 13.7493C9.12414 13.7493 8.83653 13.4532 8.83653 13.0725V5.63698C8.83653 5.25632 9.12414 4.9518 9.51326 4.9518C9.90238 4.9518 10.1984 5.25632 10.1984 5.63698V13.0725C10.1984 13.4532 9.90238 13.7493 9.51326 13.7493ZM15.2062 12.049C14.8171 12.049 14.5295 11.7445 14.5295 11.3723V7.33727C14.5295 6.96506 14.8171 6.65208 15.2062 6.65208C15.5869 6.65208 15.8914 6.96506 15.8914 7.33727V11.3723C15.8914 11.7445 15.5869 12.049 15.2062 12.049ZM0.969558 11.3215C0.588898 11.3215 0.301289 11.017 0.301289 10.6363V8.07321C0.301289 7.69255 0.588898 7.37956 0.969558 7.37956C1.36714 7.37956 1.66321 7.69255 1.66321 8.07321V10.6363C1.66321 11.017 1.36714 11.3215 0.969558 11.3215Z" fill="#191A1D"/>
          </svg>
        </div>
      </div>
      <ReactH5AudioPlayer
        className="custom-h5-player"
        onPlay={handlePlay}
        onPause={handlePause}
        src={src}
        showFilledVolume={true}
        customAdditionalControls={[]}
        customVolumeControls={[]}
        customProgressBarSection={[
          RHAP_UI.CURRENT_TIME,
          RHAP_UI.PROGRESS_BAR,
          RHAP_UI.DURATION,
        ]}
        {...props}
      />
    </div>
  );
}
