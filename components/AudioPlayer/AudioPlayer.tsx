"use client";

import React, { useState } from "react";
import ReactH5AudioPlayer, {
  RHAP_UI,
} from "react-h5-audio-player";
import "react-h5-audio-player/src/styles.scss";
import "./AudioPlayer.css";
import Image from "next/image";

interface CustomAudioPlayerProps {
  title?: string;
  subtitle?: string;
  evaluatorName?: string;
  coverUrl?: string;
  src?: string;
}

export default function CustomAudioPlayer({
  title = "Sales Call With Zane",
  subtitle = "Call Recording",
  evaluatorName = "System",
  coverUrl = "/avatar.svg",
  src = '',
  ...props
}: CustomAudioPlayerProps) {
  const [, setIsPlaying] = useState(false);
  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);

  return (
    <div className="custom-audio-container">
      <div className="header">
        <Image
          src={coverUrl}
          width={50}
          height={50}
          alt={"User"}
        />
        <div className="audio-text">
          <div className="audio-title">{title}</div>
          <div className="audio-subtitle">Evaluator: {evaluatorName}</div>
        </div>
        <div className="sound-wave-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2V22M8 6V18M16 6V18M4 10V14M20 10V14" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </div>
      </div>
      <ReactH5AudioPlayer
        className="custom-h5-player"
        onPlay={handlePlay}
        onPause={handlePause}
        src={src}
        showFilledVolume={true}
        customAdditionalControls={[]}
        customVolumeControls={[]}
        customProgressBarSection={[
          RHAP_UI.CURRENT_TIME,
          RHAP_UI.PROGRESS_BAR,
          RHAP_UI.DURATION,
        ]}
        {...props}
      />
    </div>
  );
}
